import * as THREE from "https://cdn.skypack.dev/three@0.129.0/build/three.module.js";
import { OrbitControls } from "https://cdn.skypack.dev/three@0.129.0/examples/jsm/controls/OrbitControls.js";
import { GLTFLoader } from "https://cdn.skypack.dev/three@0.129.0/examples/jsm/loaders/GLTFLoader.js";

// DOM references
const container = document.getElementById("container3D");
const loading = document.getElementById("loading");
const objToRender = container.dataset.model;

// Scene
const scene = new THREE.Scene();

// Camera with better initial position
const camera = new THREE.PerspectiveCamera(
  45, // Reduced FOV for better framing
  container.clientWidth / container.clientHeight,
  0.1,
  1000
);

// Renderer with improved settings for natural colors
const renderer = new THREE.WebGLRenderer({
  alpha: true,
  antialias: true,
  powerPreference: "high-performance",
});
renderer.setSize(container.clientWidth, container.clientHeight);
renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

// Use standard color space without tone mapping for natural colors
renderer.outputColorSpace = THREE.LinearSRGBColorSpace; // Changed from SRGBColorSpace to LinearSRGBColorSpace
// Completely remove tone mapping to show natural colors
// renderer.toneMapping = THREE.ACESFilmicToneMapping;
// renderer.toneMappingExposure = 1.0;
renderer.shadowMap.enabled = true;
renderer.shadowMap.type = THREE.PCFSoftShadowMap;

container.appendChild(renderer.domElement);

// Natural lighting setup with proper intensity
const ambientLight = new THREE.AmbientLight(0xffffff, 2.0); // Increased intensity further
scene.add(ambientLight);

// Main directional light with natural intensity
const directionalLight = new THREE.DirectionalLight(0xffffff, 2.0); // Increased intensity further
directionalLight.position.set(10, 10, 5);
directionalLight.castShadow = true;
directionalLight.shadow.mapSize.width = 2048;
directionalLight.shadow.mapSize.height = 2048;
scene.add(directionalLight);

// Secondary fill light to reduce harsh shadows
const fillLight = new THREE.DirectionalLight(0xffffff, 1.0); // Increased intensity further
fillLight.position.set(-10, 5, -5);
scene.add(fillLight);

// Controls
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;
controls.enableZoom = true;
controls.autoRotate = false;
controls.minDistance = 1;
controls.maxDistance = 50;

// Load model with improved centering and scaling
const loader = new GLTFLoader();
loader.load(
  `./models/${objToRender}.glb`,
  function (gltf) {
    const object = gltf.scene;

    // Preserve original materials without applying filters
    object.traverse((child) => {
      if (child.isMesh) {
        // Keep original material properties without modifications
        if (child.material) {
          child.material.needsUpdate = true;
          // Remove any artificial color modifications
          if (child.material.emissive) {
            child.material.emissive.setHex(0x000000);
          }
          // Completely remove material property overrides that create dark filter effect
          // Do not modify roughness or metalness at all
          // child.material.roughness = 0.3; // Lower roughness for more light reflection
          // child.material.metalness = 0.0; // Remove metalness completely
        }
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    // Center and scale the model properly
    const box = new THREE.Box3().setFromObject(object);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());

    // Calculate appropriate scale to fit in viewport
    const maxDim = Math.max(size.x, size.y, size.z);
    const scale = 5 / maxDim; // Enlarged scale factor
    object.scale.multiplyScalar(scale);

    // Center the object
    object.position.x = -center.x * scale;
    object.position.y = -center.y * scale;
    object.position.z = -center.z * scale;

    // Position camera for optimal viewing
    const distance = maxDim * scale * 2;
    camera.position.set(distance, distance * 0.7, distance);
    camera.lookAt(0, 0, 0);

    // Update controls target
    controls.target.set(0, 0, 0);
    controls.update();

    scene.add(object);
    loading.style.display = "none";
  },
  function (xhr) {
    if (xhr.lengthComputable) {
      const percentComplete = (xhr.loaded / xhr.total) * 100;
      console.log(Math.round(percentComplete) + "% loaded");
    }
  },
  function (error) {
    console.error("Error loading model:", error);
    loading.textContent =
      "Failed to load model. Please check if the model file exists.";
  }
);

// Resize handler
function onWindowResize() {
  const width = container.clientWidth;
  const height = container.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

window.addEventListener("resize", onWindowResize);

// Animation loop with controls damping
function animate() {
  requestAnimationFrame(animate);
  controls.update();
  renderer.render(scene, camera);
}

animate();
