<!DOCTYPE html>
<html lang="el">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Polis Chrysochous Museum</title>
  <link rel="stylesheet" href="css/style.css">
</head>

<body>
  <header>
    <div class="logo-title">
      <img src="images/logo.png" alt="Logo" class="logo">
      <h1>Weight</h1>
    </div>
  </header>

  <main>
    <!-- Photo viewer with navigation -->
    <div class="photo-viewer">
      <div class="photo-container">
        <img id="current-photo" src="images/12.jpg" alt="Weight - View 1">
        <button id="prev-photo" class="nav-btn">❮</button>
        <button id="next-photo" class="nav-btn">❯</button>
        <div class="photo-nav">
          <span id="photo-counter">1 / 1</span>
        </div>
      </div>
    </div>

    <!-- Language toggle -->
    <div class="lang-switcher">
      <button id="langToggle">GR / EN</button>
    </div>

    <!-- Greek Description -->
    <div class="container lang lang-gr">
      <h2>Βαρίδιο</h2>
      <div class="section">
        <div class="section-title">1. Τύπος αντικειμένου:</div>
        <div class="section-content">Βαρίδιο</div>
      </div>
      <div class="section">
        <div class="section-title">2. Υλικά και τεχνικές:</div>
        <div class="section-content">
          Μολύβδινο
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Διαστάσεις:</div>
        <div class="section-content">5 εκ.</div>
      </div>
      <div class="section">
        <div class="section-title">4. Χρονολόγηση:</div>
        <div class="section-content">1 π.Χ. – 1 μ.Χ.</div>
      </div>
      <div class="section">
        <div class="section-title">5. Περιοχή προέλευσης:</div>
        <div class="section-content">Μάριον.</div>
      </div>
      <div class="section">
        <div class="section-title">6. Περιγραφή:</div>
        <div class="section-content">
          Μολύβδινο, κωνικό βαρίδιο με τετράπλευρη βάση. Συχνά τα μολύβδινα βαρίδια χρησιμοποιούνταν ως βάρη ψαρέματος ή
          διχτυών.
        </div>
      </div>
    </div>

    <!-- English Description -->
    <div class="container lang lang-en" style="display: none;">
      <h2>Weight</h2>
      <div class="section">
        <div class="section-title">1. Type of object:</div>
        <div class="section-content">Weight</div>
      </div>
      <div class="section">
        <div class="section-title">2. Materials and techniques:</div>
        <div class="section-content">
          Lead
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Measurements:</div>
        <div class="section-content">
          5 cm
        </div>
      </div>
      <div class="section">
        <div class="section-title">4. Date or period:</div>
        <div class="section-content">Roman period 1 BC – 1 AD</div>
      </div>
      <div class="section">
        <div class="section-title">5. Place of origin:</div>
        <div class="section-content">Marion</div>
      </div>
      <div class="section">
        <div class="section-title">6. Description:</div>
        <div class="section-content">
          Lead weight, conical in shape with a quadrilateral base. Often the lead weights were used as fishing or net
          weights.
        </div>
      </div>
    </div>

    <!-- 3D viewer removed -->
  </main>

  <script>
    // Photo navigation
    const photos = [
      { src: 'images/12.jpg', alt: 'Weight - View 1' },
    ];
    let currentPhotoIndex = 0;

    const currentPhoto = document.getElementById('current-photo');
    const photoCounter = document.getElementById('photo-counter');
    const prevBtn = document.getElementById('prev-photo');
    const nextBtn = document.getElementById('next-photo');

    function updatePhoto() {
      currentPhoto.src = photos[currentPhotoIndex].src;
      currentPhoto.alt = photos[currentPhotoIndex].alt;
      photoCounter.textContent = `${currentPhotoIndex + 1} / ${photos.length}`;
    }

    prevBtn.addEventListener('click', () => {
      currentPhotoIndex = (currentPhotoIndex - 1 + photos.length) % photos.length;
      updatePhoto();
    });

    nextBtn.addEventListener('click', () => {
      currentPhotoIndex = (currentPhotoIndex + 1) % photos.length;
      updatePhoto();
    });

    // Language toggle
    const langToggle = document.getElementById('langToggle');
    langToggle.addEventListener('click', () => {
      const gr = document.querySelector('.lang-gr');
      const en = document.querySelector('.lang-en');
      const isGR = gr.style.display !== 'none';
      gr.style.display = isGR ? 'none' : 'block';
      en.style.display = isGR ? 'block' : 'none';
    });
  </script>
</body>

</html>