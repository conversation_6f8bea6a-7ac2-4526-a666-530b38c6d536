<!DOCTYPE html>
<html lang="el">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Polis Chrysochous Museum</title>
  <link rel="stylesheet" href="css/style.css">
</head>

<body>
  <header>
    <div class="logo-title">
      <img src="images/logo.png" alt="Logo" class="logo">
      <h1>Coin</h1>
    </div>
  </header>

  <main>
    <!-- Photo viewer with navigation -->
    <div class="photo-viewer">
      <div class="photo-container">
        <img id="current-photo" src="images/15a-front.jpg" alt="Roman Coin - Front">
        <button id="prev-photo" class="nav-btn">❮</button>
        <button id="next-photo" class="nav-btn">❯</button>
        <div class="photo-nav">
          <span id="photo-counter">1 / 2</span>
        </div>
      </div>
    </div>

    <!-- Language toggle -->
    <div class="lang-switcher">
      <button id="langToggle">GR / EN</button>
    </div>

    <!-- Greek Description -->
    <div class="container lang lang-gr">
      <h2>Νόμισμα</h2>
      <div class="section">
        <div class="section-title">1. Τύπος αντικειμένου:</div>
        <div class="section-content">Νόμισμα</div>
      </div>
      <div class="section">
        <div class="section-title">2. Υλικά και τεχνικές:</div>
        <div class="section-content">
          Χάλκινο. Κοπή
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Χρονολόγηση:</div>
        <div class="section-content">Περίοδος Ρωμαϊκής Δημοκρατίας, Περίπου 3ος αιώνας π.Χ.</div>
      </div>
      <div class="section">
        <div class="section-title">4. Περιοχή προέλευσης:</div>
        <div class="section-content">Ρώμη</div>
      </div>
      <div class="section">
        <div class="section-title">5. Περιγραφή:</div>
        <div class="section-content">
          Ρωμαϊκό χάλκινο νόμισμα (αξίας τετάρτου – quadran) της Ρωμαϊκής Δημοκρατίας. Στην εμπρόσθια όψη υπάρχει κεφάλι
          ανδρικής
          μορφής που κοιτάζει προς τα δεξιά. Στην οπίσθια όψη υπάρχει η πλώρη γαλέρας στραμμένη προς τα δεξιά και 3
          σφαιρίδα κάτω
          από αυτή.
          <br>
          Το νόμισμα ακολουθεί τον τύπο RRC 41/8b (παρόμοιο με το RRC 56/5) της Αμερικανικής Νομισματικής Εταιρείας
          (MANTIS). Η
          διάβρωση καθιστά αδύνατη την ανάγνωση οποιασδήποτε πιθανής επιγραφής.
        </div>
      </div>
    </div>

    <!-- English Description -->
    <div class="container lang lang-en" style="display: none;">
      <h2>Coin</h2>
      <div class="section">
        <div class="section-title">1. Type of object:</div>
        <div class="section-content">Coin</div>
      </div>
      <div class="section">
        <div class="section-title">2. Materials and techniques:</div>
        <div class="section-content">
          Bronze. Struck
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Date or period:</div>
        <div class="section-content">Roman Republic period, ca end of 3rd century BC</div>
      </div>
      <div class="section">
        <div class="section-title">4. Place of origin:</div>
        <div class="section-content">Rome</div>
      </div>
      <div class="section">
        <div class="section-title">5. Description:</div>
        <div class="section-content">
          Roman bronze quadran of the Roman Republic. On the observe side there is a male head facing right. On the
          reverse side
          there is a prow of a galley facing right and 3 pellets below it.
          <br>
          The coin follows the type RRC 41/8b (similar to RRC 56/5) as of the American Numismatic Society (MANTIS). The
          ccorrosion
          does not allow the reading of any possible inscriptions.
        </div>
      </div>
    </div>

    <!-- 3D viewer removed -->
  </main>

  <script>
    // Photo navigation
    const photos = [
      { src: 'images/15a-front.jpg', alt: 'Roman Coin - Front' },
      { src: 'images/15a-reverse.jpg', alt: 'Roman Coin - Reverse' }
    ];
    let currentPhotoIndex = 0;

    const currentPhoto = document.getElementById('current-photo');
    const photoCounter = document.getElementById('photo-counter');
    const prevBtn = document.getElementById('prev-photo');
    const nextBtn = document.getElementById('next-photo');

    function updatePhoto() {
      currentPhoto.src = photos[currentPhotoIndex].src;
      currentPhoto.alt = photos[currentPhotoIndex].alt;
      photoCounter.textContent = `${currentPhotoIndex + 1} / ${photos.length}`;
    }

    prevBtn.addEventListener('click', () => {
      currentPhotoIndex = (currentPhotoIndex - 1 + photos.length) % photos.length;
      updatePhoto();
    });

    nextBtn.addEventListener('click', () => {
      currentPhotoIndex = (currentPhotoIndex + 1) % photos.length;
      updatePhoto();
    });

    // Language toggle
    const langToggle = document.getElementById('langToggle');
    langToggle.addEventListener('click', () => {
      const gr = document.querySelector('.lang-gr');
      const en = document.querySelector('.lang-en');
      const isGR = gr.style.display !== 'none';
      gr.style.display = isGR ? 'none' : 'block';
      en.style.display = isGR ? 'block' : 'none';
    });
  </script>
</body>

</html>